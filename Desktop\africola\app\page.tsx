"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence, useScroll, useTransform, useInView } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Zap,
  Sparkles,
  MapPin,
  ShoppingCart,
  Users,
  Play,
  ChevronDown,
  Instagram,
  Facebook,
  Twitter,
  Star,
  Flame,
  Heart,
  Globe,
  Award,
  Truck,
  Phone,
} from "lucide-react"
import Image from "next/image"

const products = [
  {
    id: "kola-nut",
    name: "KOLA NUT",
    tagline: "The Original Energy",
    description: "Classic kola nut taste, explosive energy that ignites your soul",
    color: "#FFCC00",
    textColor: "#111111",
    image: "/images/africola-kola-nut.jpg",
    icon: <Zap className="w-8 h-8" />,
    features: ["300mg Caffeine", "Natural Kola Extract", "Instant Energy Boost", "African Heritage"],
    lifestyle: "Perfect for gym sessions, late-night studies, and adventure seekers",
    gradient: "from-yellow-300 via-yellow-400 to-orange-500",
  },
  {
    id: "classic",
    name: "CLASSIC",
    tagline: "The Bold Classic",
    description: "Traditional cola with Mediterranean passion and Italian sophistication",
    color: "#C8102E",
    textColor: "#FFFFFF",
    image: "/placeholder.svg?height=500&width=350",
    icon: <Flame className="w-8 h-8" />,
    features: ["Bold Cola Taste", "139 Kcal Energy", "Italian Inspired", "Premium Quality"],
    lifestyle: "Ideal for social gatherings, romantic dinners, and cultural experiences",
    gradient: "from-red-500 via-red-600 to-pink-600",
  },
  {
    id: "light",
    name: "LIGHT",
    tagline: "The Fresh Spark",
    description: "Zero sugar refreshment with the lightness of Mediterranean breeze",
    color: "#D3D3D3",
    textColor: "#111111",
    image: "/images/africola-light.jpg",
    icon: <Sparkles className="w-8 h-8" />,
    features: ["Zero Sugar", "0.5 Kcal Only", "Fresh & Light", "Guilt-Free Energy"],
    lifestyle: "Perfect for health-conscious individuals, beach days, and wellness journeys",
    gradient: "from-gray-300 via-gray-400 to-blue-400",
  },
]

const lifestyleImages = [
  {
    title: "Gym Warriors",
    image: "/placeholder.svg?height=400&width=600&text=Gym+Workout+Energy",
    description: "Fuel your fitness journey",
    category: "FITNESS",
  },
  {
    title: "Beach Vibes",
    image: "/placeholder.svg?height=400&width=600&text=Mediterranean+Beach+Life",
    description: "Mediterranean summer energy",
    category: "LIFESTYLE",
  },
  {
    title: "Night Life",
    image: "/placeholder.svg?height=400&width=600&text=Tunisian+Night+Scene",
    description: "Tunisian nights come alive",
    category: "SOCIAL",
  },
  {
    title: "Creative Minds",
    image: "/placeholder.svg?height=400&width=600&text=Italian+Design+Studio",
    description: "Italian creativity flows",
    category: "CREATIVE",
  },
  {
    title: "Adventure Seekers",
    image: "/placeholder.svg?height=400&width=600&text=Sahara+Desert+Adventure",
    description: "Sahara adventures await",
    category: "ADVENTURE",
  },
  {
    title: "Urban Culture",
    image: "/placeholder.svg?height=400&width=600&text=Tunis+Street+Art",
    description: "Street culture energy",
    category: "URBAN",
  },
]

const FloatingCan = ({ product, index, isActive }: any) => {
  return (
    <motion.div
      className="absolute"
      initial={{
        x: Math.random() * 200 - 100,
        y: Math.random() * 200 - 100,
        rotate: Math.random() * 360,
        scale: 0,
      }}
      animate={{
        x: isActive ? 0 : Math.random() * 100 - 50,
        y: isActive ? 0 : Math.random() * 100 - 50,
        rotate: isActive ? 0 : Math.random() * 360,
        scale: isActive ? 1.2 : 0.3,
      }}
      transition={{
        duration: 2,
        ease: "easeInOut",
        repeat: isActive ? 0 : Number.POSITIVE_INFINITY,
        repeatType: "reverse",
      }}
    >
      <Image
        src={product.image || "/placeholder.svg"}
        alt={product.name}
        width={200}
        height={300}
        className="drop-shadow-2xl"
      />
    </motion.div>
  )
}

const ParallaxSection = ({ children, offset = 50 }: any) => {
  const ref = useRef(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  })
  const y = useTransform(scrollYProgress, [0, 1], [offset, -offset])

  return (
    <motion.div ref={ref} style={{ y }}>
      {children}
    </motion.div>
  )
}

export default function AfriColaCreativeWebsite() {
  const [currentProduct, setCurrentProduct] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const heroRef = useRef(null)
  const isHeroInView = useInView(heroRef)

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }
    window.addEventListener("mousemove", handleMouseMove)
    return () => window.removeEventListener("mousemove", handleMouseMove)
  }, [])

  useEffect(() => {
    if (!isAutoPlaying) return
    const interval = setInterval(() => {
      setCurrentProduct((prev) => (prev + 1) % products.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const currentBg = products[currentProduct].color

  return (
    <div className="min-h-screen bg-black overflow-hidden relative">
      {/* Animated Background Particles */}
      <div className="fixed inset-0 z-0">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/10 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Dynamic Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/95 backdrop-blur-xl border-b border-white/10">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <motion.div
            className="text-3xl font-black text-white relative"
            whileHover={{ scale: 1.1 }}
            style={{
              background: `linear-gradient(45deg, ${currentBg}, #ffffff)`,
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            AFRI<span className="text-red-500">COLA</span>
            <motion.div
              className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
            />
          </motion.div>

          <div className="hidden md:flex space-x-8 text-white">
            {["Home", "Products", "Culture", "Lifestyle", "Contact"].map((item) => (
              <motion.a
                key={item}
                href={`#${item.toLowerCase()}`}
                className="relative hover:text-yellow-400 transition-colors font-semibold"
                whileHover={{ y: -2 }}
              >
                {item}
                <motion.div
                  className="absolute -bottom-1 left-0 w-0 h-0.5 bg-yellow-400"
                  whileHover={{ width: "100%" }}
                  transition={{ duration: 0.3 }}
                />
              </motion.a>
            ))}
          </div>

          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button className="bg-gradient-to-r from-yellow-400 to-red-500 text-black font-bold hover:from-yellow-500 hover:to-red-600">
              <ShoppingCart className="w-4 h-4 mr-2" />
              Order Now
            </Button>
          </motion.div>
        </div>
      </nav>

      {/* Epic Hero Section */}
      <section ref={heroRef} id="home" className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Dynamic Background */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentProduct}
            className={`absolute inset-0 bg-gradient-to-br ${products[currentProduct].gradient}`}
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ opacity: 0.9, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 1.5 }}
          />
        </AnimatePresence>

        {/* Floating Elements */}
        <div className="absolute inset-0">
          {products.map((product, index) => (
            <FloatingCan key={product.id} product={product} index={index} isActive={index === currentProduct} />
          ))}
        </div>

        {/* Interactive Mouse Follower */}
        <motion.div
          className="fixed w-20 h-20 border-2 border-white/30 rounded-full pointer-events-none z-30"
          animate={{
            x: mousePosition.x - 40,
            y: mousePosition.y - 40,
          }}
          transition={{ type: "spring", stiffness: 500, damping: 28 }}
        />

        <div className="relative z-20 container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5 }}
          >
            <motion.h1
              className="text-8xl md:text-9xl font-black mb-6 leading-none"
              style={{ color: products[currentProduct].textColor }}
              animate={{
                textShadow: `0 0 20px ${currentBg}50`,
              }}
            >
              ONE BRAND.
              <br />
              <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                THREE POWERS.
              </span>
            </motion.h1>

            <motion.div
              className="text-3xl md:text-4xl font-bold mb-8 opacity-90"
              style={{ color: products[currentProduct].textColor }}
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY }}
            >
              🇹🇳 ⚡ 🇮🇹 TWO SHORES. ONE ENERGY. 🇮🇹 ⚡ 🇹🇳
            </motion.div>

            <AnimatePresence mode="wait">
              <motion.div
                key={currentProduct}
                className="mb-12"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -30 }}
              >
                <h2 className="text-4xl font-bold mb-4" style={{ color: products[currentProduct].textColor }}>
                  {products[currentProduct].name} - {products[currentProduct].tagline}
                </h2>
                <p
                  className="text-xl opacity-80 max-w-2xl mx-auto"
                  style={{ color: products[currentProduct].textColor }}
                >
                  {products[currentProduct].description}
                </p>
              </motion.div>
            </AnimatePresence>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Button size="lg" className="bg-black text-white hover:bg-gray-800 text-xl px-12 py-6 rounded-full">
                  <Zap className="w-6 h-6 mr-3" />
                  TASTE THE ENERGY
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2 border-black text-black hover:bg-black hover:text-white text-xl px-12 py-6 rounded-full bg-transparent"
                >
                  <Play className="w-6 h-6 mr-3" />
                  WATCH STORY
                </Button>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Product Selector with Enhanced Design */}
        <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 flex space-x-6">
          {products.map((product, index) => (
            <motion.button
              key={product.id}
              onClick={() => {
                setCurrentProduct(index)
                setIsAutoPlaying(false)
              }}
              className={`relative p-4 rounded-full transition-all ${
                index === currentProduct ? "bg-white/20 backdrop-blur-sm scale-125" : "bg-white/10 hover:bg-white/15"
              }`}
              whileHover={{ scale: index === currentProduct ? 1.25 : 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <div className="w-6 h-6 rounded-full" style={{ backgroundColor: product.color }} />
              {index === currentProduct && (
                <motion.div
                  className="absolute inset-0 border-2 border-white rounded-full"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.3 }}
                />
              )}
            </motion.button>
          ))}
        </div>

        <motion.div
          className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 15, 0] }}
          transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
        >
          <ChevronDown className="w-10 h-10 text-white/70" />
        </motion.div>
      </section>

      {/* Revolutionary Products Section */}
      <section id="products" className="py-32 bg-gradient-to-b from-black to-gray-900 relative">
        <ParallaxSection>
          <div className="container mx-auto px-4">
            <motion.div
              className="text-center mb-20"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            >
              <h2 className="text-7xl font-black text-white mb-6">
                THREE{" "}
                <span className="bg-gradient-to-r from-yellow-400 to-red-500 bg-clip-text text-transparent">
                  LEGENDS
                </span>
              </h2>
              <p className="text-2xl text-gray-300 max-w-3xl mx-auto">
                Each can tells a story. Each sip ignites a journey. Choose your power, embrace your destiny.
              </p>
            </motion.div>

            <div className="grid lg:grid-cols-3 gap-12">
              {products.map((product, index) => (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 100, rotateY: 45 }}
                  whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.3, duration: 0.8 }}
                  whileHover={{ y: -20, rotateY: 5 }}
                  className="group"
                >
                  <Card className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 overflow-hidden hover:border-white/40 transition-all duration-500">
                    <div
                      className="h-40 flex items-center justify-center relative overflow-hidden"
                      style={{ background: `linear-gradient(135deg, ${product.color}20, ${product.color}40)` }}
                    >
                      <motion.div
                        className="absolute inset-0 opacity-20"
                        animate={{
                          background: [
                            `radial-gradient(circle at 20% 50%, ${product.color}40 0%, transparent 50%)`,
                            `radial-gradient(circle at 80% 50%, ${product.color}40 0%, transparent 50%)`,
                            `radial-gradient(circle at 20% 50%, ${product.color}40 0%, transparent 50%)`,
                          ],
                        }}
                        transition={{ duration: 4, repeat: Number.POSITIVE_INFINITY }}
                      />
                      <h3 className="text-4xl font-black text-white z-10 group-hover:scale-110 transition-transform">
                        {product.name}
                      </h3>
                      <motion.div
                        className="absolute top-4 right-4 text-white/60"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 8, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                      >
                        {product.icon}
                      </motion.div>
                    </div>

                    <CardContent className="p-8">
                      <div className="flex justify-center mb-6 relative">
                        <motion.div whileHover={{ scale: 1.1, rotateY: 15 }} transition={{ duration: 0.3 }}>
                          <Image
                            src={product.image || "/placeholder.svg"}
                            alt={product.name}
                            width={180}
                            height={250}
                            className="object-contain drop-shadow-2xl"
                          />
                        </motion.div>
                        <motion.div
                          className="absolute -top-2 -right-2 bg-yellow-400 text-black px-3 py-1 rounded-full text-sm font-bold"
                          animate={{ rotate: [0, 5, -5, 0] }}
                          transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                        >
                          NEW
                        </motion.div>
                      </div>

                      <h4 className="text-2xl font-bold text-white mb-3">{product.tagline}</h4>
                      <p className="text-gray-300 mb-6 leading-relaxed">{product.description}</p>
                      <p className="text-gray-400 mb-6 italic">{product.lifestyle}</p>

                      <div className="flex flex-wrap gap-2 mb-6">
                        {product.features.map((feature) => (
                          <Badge
                            key={feature}
                            className="bg-white/10 text-white border-white/20 hover:bg-white/20 transition-colors"
                          >
                            {feature}
                          </Badge>
                        ))}
                      </div>

                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          className="w-full text-lg py-6 rounded-xl font-bold"
                          style={{
                            background: `linear-gradient(135deg, ${product.color}, ${product.color}CC)`,
                            color: product.textColor,
                          }}
                        >
                          <Star className="w-5 h-5 mr-2" />
                          EXPERIENCE {product.name}
                        </Button>
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </ParallaxSection>
      </section>

      {/* Cultural Fusion Masterpiece */}
      <section
        id="culture"
        className="py-32 bg-gradient-to-r from-yellow-400 via-red-500 to-gray-400 relative overflow-hidden"
      >
        <div className="absolute inset-0">
          <Image
            src="/placeholder.svg?height=800&width=1200&text=Mediterranean+Sea+Waves"
            alt="Mediterranean Sea"
            fill
            className="object-cover opacity-20"
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -100 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 1 }}
            >
              <motion.h2
                className="text-7xl font-black text-white mb-8 leading-tight"
                animate={{
                  textShadow: [
                    "0 0 20px rgba(255,255,255,0.5)",
                    "0 0 40px rgba(255,255,255,0.8)",
                    "0 0 20px rgba(255,255,255,0.5)",
                  ],
                }}
                transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY }}
              >
                TWO SHORES.
                <br />
                ONE ENERGY.
              </motion.h2>

              <div className="text-xl text-white/95 mb-8 leading-relaxed space-y-4">
                <p>
                  🏺 <strong>From the ancient souks of Tunis</strong> to the Renaissance studios of Milan, AfriCola
                  bridges two magnificent cultures with one electrifying taste.
                </p>
                <p>
                  🌊 <strong>The Mediterranean Sea</strong> doesn't divide us—it connects us. Every wave carries the
                  spirit of innovation from Italian shores to Tunisian hearts.
                </p>
                <p>
                  ⚡ <strong>AfriCola is that connection</strong>—the liquid bridge between North African authenticity
                  and European sophistication.
                </p>
              </div>

              <div className="flex flex-wrap gap-4 mb-8">
                {[
                  { icon: <Award className="w-5 h-5" />, text: "Tunisian Heritage", color: "bg-green-600" },
                  { icon: <Star className="w-5 h-5" />, text: "Italian Design", color: "bg-red-600" },
                  { icon: <Globe className="w-5 h-5" />, text: "Mediterranean Spirit", color: "bg-blue-600" },
                  { icon: <Heart className="w-5 h-5" />, text: "Cultural Fusion", color: "bg-purple-600" },
                ].map((badge, index) => (
                  <motion.div
                    key={badge.text}
                    className={`${badge.color} text-white px-4 py-2 rounded-full flex items-center space-x-2 font-semibold`}
                    initial={{ opacity: 0, scale: 0 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.1 }}
                  >
                    {badge.icon}
                    <span>{badge.text}</span>
                  </motion.div>
                ))}
              </div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button size="lg" className="bg-black text-white hover:bg-gray-800 text-xl px-8 py-6 rounded-full">
                  <Globe className="w-6 h-6 mr-3" />
                  DISCOVER OUR JOURNEY
                </Button>
              </motion.div>
            </motion.div>

            <motion.div
              className="relative"
              initial={{ opacity: 0, x: 100 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 1 }}
            >
              <div className="bg-black/20 backdrop-blur-xl rounded-3xl p-8 border border-white/20">
                <div className="grid grid-cols-2 gap-8 mb-8">
                  <motion.div className="text-center" whileHover={{ scale: 1.05 }}>
                    <div className="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MapPin className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-white font-bold text-xl mb-2">TUNISIA</h3>
                    <p className="text-white/80">Authentic Heritage</p>
                    <p className="text-white/60 text-sm mt-2">Where tradition meets innovation</p>
                  </motion.div>

                  <motion.div className="text-center" whileHover={{ scale: 1.05 }}>
                    <div className="w-20 h-20 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MapPin className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-white font-bold text-xl mb-2">ITALY</h3>
                    <p className="text-white/80">Creative Excellence</p>
                    <p className="text-white/60 text-sm mt-2">Design that speaks to the soul</p>
                  </motion.div>
                </div>

                <div className="text-center">
                  <motion.div
                    className="w-24 h-24 bg-gradient-to-r from-yellow-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 10, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                  >
                    <Zap className="w-12 h-12 text-white" />
                  </motion.div>
                  <h3 className="text-white font-bold text-2xl mb-2">AFRICOLA</h3>
                  <p className="text-white/80 text-lg">The Energy Bridge</p>
                  <p className="text-white/60 mt-2">Connecting cultures, igniting passions</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Lifestyle Gallery Explosion */}
      <section id="lifestyle" className="py-32 bg-gradient-to-b from-gray-900 to-black">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-7xl font-black text-white mb-6">
              LIFESTYLE{" "}
              <span className="bg-gradient-to-r from-yellow-400 to-red-500 bg-clip-text text-transparent">ENERGY</span>
            </h2>
            <p className="text-2xl text-gray-300 max-w-4xl mx-auto">
              From the bustling souks of Tunis to the vibrant piazzas of Rome, AfriCola fuels every moment of your
              Mediterranean lifestyle.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {lifestyleImages.map((item, index) => (
              <motion.div
                key={item.title}
                className="relative group cursor-pointer overflow-hidden rounded-2xl"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
              >
                <div className="aspect-[4/3] relative">
                  <Image
                    src={item.image || "/placeholder.svg"}
                    alt={item.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />

                  <motion.div
                    className="absolute top-4 right-4 bg-yellow-400 text-black px-3 py-1 rounded-full text-sm font-bold"
                    animate={{ rotate: [0, 5, -5, 0] }}
                    transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY, delay: index * 0.2 }}
                  >
                    {item.category}
                  </motion.div>

                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <h3 className="text-white font-bold text-2xl mb-2">{item.title}</h3>
                    <p className="text-white/80 mb-4">{item.description}</p>

                    <motion.div className="flex items-center text-yellow-400 font-semibold" whileHover={{ x: 10 }}>
                      <Play className="w-5 h-5 mr-2" />
                      Watch Experience
                    </motion.div>
                  </div>
                </div>

                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-red-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  initial={false}
                />
              </motion.div>
            ))}
          </div>

          {/* Lifestyle Stats */}
          <motion.div
            className="mt-20 grid md:grid-cols-4 gap-8"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            {[
              { icon: <Users className="w-8 h-8" />, number: "1M+", label: "Energy Seekers" },
              { icon: <Globe className="w-8 h-8" />, number: "25+", label: "Countries" },
              { icon: <Star className="w-8 h-8" />, number: "4.9", label: "Rating" },
              { icon: <Zap className="w-8 h-8" />, number: "∞", label: "Energy Levels" },
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                initial={{ opacity: 0, scale: 0 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.1 }}
              >
                <div className="text-yellow-400 mb-4 flex justify-center">{stat.icon}</div>
                <div className="text-4xl font-black text-white mb-2">{stat.number}</div>
                <div className="text-gray-400">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact & Distribution Hub */}
      <section id="contact" className="py-32 bg-gradient-to-br from-black via-gray-900 to-red-900 relative">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="/placeholder.svg?height=800&width=1200&text=Tunis+Cityscape+Night"
            alt="Tunis Night"
            fill
            className="object-cover"
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16">
            <motion.div initial={{ opacity: 0, x: -50 }} whileInView={{ opacity: 1, x: 0 }} viewport={{ once: true }}>
              <h2 className="text-6xl font-black text-white mb-8">
                JOIN THE <span className="text-yellow-400">ENERGY</span> REVOLUTION
              </h2>

              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Ready to bring the Mediterranean energy to your community? Let's create something extraordinary
                together.
              </p>

              <div className="space-y-6 mb-8">
                {[
                  {
                    icon: <MapPin className="w-6 h-6" />,
                    text: "Jnene Hammammet N° 695, Tunisia",
                    color: "text-green-400",
                  },
                  { icon: <Phone className="w-6 h-6" />, text: "+216 XX XXX XXX", color: "text-blue-400" },
                  {
                    icon: <Users className="w-6 h-6" />,
                    text: "Become a Premium Distributor",
                    color: "text-yellow-400",
                  },
                  { icon: <Truck className="w-6 h-6" />, text: "Nationwide Delivery Available", color: "text-red-400" },
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center space-x-4"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ x: 10 }}
                  >
                    <div className={`${item.color}`}>{item.icon}</div>
                    <span className="text-white text-lg">{item.text}</span>
                  </motion.div>
                ))}
              </div>

              <div className="flex space-x-4">
                {[
                  { icon: <Instagram className="w-6 h-6" />, color: "bg-pink-600" },
                  { icon: <Facebook className="w-6 h-6" />, color: "bg-blue-600" },
                  { icon: <Twitter className="w-6 h-6" />, color: "bg-sky-500" },
                ].map((social, index) => (
                  <motion.button
                    key={index}
                    className={`${social.color} p-3 rounded-full text-white hover:scale-110 transition-transform`}
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    {social.icon}
                  </motion.button>
                ))}
              </div>
            </motion.div>

            <motion.div initial={{ opacity: 0, x: 50 }} whileInView={{ opacity: 1, x: 0 }} viewport={{ once: true }}>
              <Card className="bg-white/10 backdrop-blur-xl border border-white/20">
                <CardContent className="p-8">
                  <h3 className="text-3xl font-bold text-white mb-6">Get In Touch</h3>
                  <div className="space-y-6">
                    <div>
                      <input
                        type="text"
                        placeholder="Your Name"
                        className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <input
                        type="email"
                        placeholder="Your Email"
                        className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <select className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent">
                        <option value="">Select Interest</option>
                        <option value="distributor">Become a Distributor</option>
                        <option value="retail">Retail Partnership</option>
                        <option value="general">General Inquiry</option>
                        <option value="media">Media & Press</option>
                      </select>
                    </div>
                    <div>
                      <textarea
                        placeholder="Tell us about your vision..."
                        rows={4}
                        className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent resize-none"
                      />
                    </div>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button className="w-full bg-gradient-to-r from-yellow-400 to-red-500 text-black font-bold text-lg py-6 rounded-xl hover:from-yellow-500 hover:to-red-600">
                        <Zap className="w-5 h-5 mr-2" />
                        IGNITE THE PARTNERSHIP
                      </Button>
                    </motion.div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Epic Footer */}
      <footer className="bg-black text-white py-16 border-t border-white/10">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-5 gap-8 mb-12">
            <div className="md:col-span-2">
              <motion.h3 className="text-4xl font-black mb-4" whileHover={{ scale: 1.05 }}>
                AFRI<span className="text-red-500">COLA</span>
              </motion.h3>
              <p className="text-gray-400 mb-6 text-lg">Two Shores. One Energy. 🇹🇳⚡🇮🇹</p>
              <p className="text-gray-500 leading-relaxed">
                Bridging cultures, igniting passions, and fueling dreams across the Mediterranean. From Tunisia's
                authentic heritage to Italy's creative excellence.
              </p>
            </div>

            {[
              {
                title: "Products",
                items: ["Kola Nut Energy", "Classic Cola", "Light & Fresh", "Limited Editions"],
              },
              {
                title: "Company",
                items: ["Our Story", "Cultural Mission", "Sustainability", "Careers"],
              },
              {
                title: "Connect",
                items: ["Instagram", "Facebook", "TikTok", "YouTube"],
              },
            ].map((section, index) => (
              <div key={section.title}>
                <h4 className="font-bold text-xl mb-4 text-yellow-400">{section.title}</h4>
                <ul className="space-y-3">
                  {section.items.map((item) => (
                    <li key={item}>
                      <motion.a
                        href="#"
                        className="text-gray-400 hover:text-white transition-colors"
                        whileHover={{ x: 5 }}
                      >
                        {item}
                      </motion.a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="border-t border-white/10 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 mb-4 md:mb-0">
              &copy; 2024 AfriCola International. Crafted with ❤️ in Tunisia & Italy.
            </p>
            <div className="flex space-x-6 text-gray-400">
              <a href="#" className="hover:text-white transition-colors">
                Privacy
              </a>
              <a href="#" className="hover:text-white transition-colors">
                Terms
              </a>
              <a href="#" className="hover:text-white transition-colors">
                Cookies
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
